import { browser } from '$app/environment';

/**
 * Performance monitoring utilities for sidebar
 */

// Performance metrics tracking
interface PerformanceMetrics {
  toggleDuration: number[];
  renderCount: number;
  lastToggleTime: number;
  averageToggleDuration: number;
}

class SidebarPerformanceMonitor {
  private metrics: PerformanceMetrics = {
    toggleDuration: [],
    renderCount: 0,
    lastToggleTime: 0,
    averageToggleDuration: 0,
  };

  private maxMetricsHistory = 50; // Keep last 50 measurements

  // Start timing a sidebar toggle
  startToggleTimer(): number {
    if (!browser || !performance.mark) return 0;
    
    const timestamp = performance.now();
    performance.mark('sidebar-toggle-start');
    return timestamp;
  }

  // End timing a sidebar toggle
  endToggleTimer(startTime: number): number {
    if (!browser || !performance.mark || !performance.measure) return 0;
    
    try {
      performance.mark('sidebar-toggle-end');
      performance.measure('sidebar-toggle-duration', 'sidebar-toggle-start', 'sidebar-toggle-end');
      
      const duration = performance.now() - startTime;
      this.recordToggleDuration(duration);
      
      return duration;
    } catch (error) {
      console.warn('Performance measurement failed:', error);
      return 0;
    }
  }

  // Record a toggle duration
  private recordToggleDuration(duration: number): void {
    this.metrics.toggleDuration.push(duration);
    this.metrics.lastToggleTime = duration;
    
    // Keep only recent measurements
    if (this.metrics.toggleDuration.length > this.maxMetricsHistory) {
      this.metrics.toggleDuration.shift();
    }
    
    // Calculate average
    this.metrics.averageToggleDuration = 
      this.metrics.toggleDuration.reduce((sum, d) => sum + d, 0) / 
      this.metrics.toggleDuration.length;
  }

  // Increment render count
  incrementRenderCount(): void {
    this.metrics.renderCount++;
  }

  // Get current metrics
  getMetrics(): Readonly<PerformanceMetrics> {
    return { ...this.metrics };
  }

  // Check if performance is within acceptable bounds
  isPerformanceGood(): boolean {
    const avgDuration = this.metrics.averageToggleDuration;
    const lastDuration = this.metrics.lastToggleTime;
    
    // Consider performance good if:
    // - Average toggle duration is under 300ms
    // - Last toggle was under 500ms
    return avgDuration < 300 && lastDuration < 500;
  }

  // Get performance warnings
  getPerformanceWarnings(): string[] {
    const warnings: string[] = [];
    const metrics = this.metrics;
    
    if (metrics.averageToggleDuration > 300) {
      warnings.push(`Average toggle duration is high: ${metrics.averageToggleDuration.toFixed(2)}ms`);
    }
    
    if (metrics.lastToggleTime > 500) {
      warnings.push(`Last toggle was slow: ${metrics.lastToggleTime.toFixed(2)}ms`);
    }
    
    if (metrics.renderCount > 100) {
      warnings.push(`High render count detected: ${metrics.renderCount}`);
    }
    
    return warnings;
  }

  // Reset metrics
  reset(): void {
    this.metrics = {
      toggleDuration: [],
      renderCount: 0,
      lastToggleTime: 0,
      averageToggleDuration: 0,
    };
  }

  // Log performance summary
  logPerformanceSummary(): void {
    if (!browser) return;
    
    const metrics = this.getMetrics();
    const warnings = this.getPerformanceWarnings();
    
    console.group('Sidebar Performance Summary');
    console.log('Render Count:', metrics.renderCount);
    console.log('Average Toggle Duration:', `${metrics.averageToggleDuration.toFixed(2)}ms`);
    console.log('Last Toggle Duration:', `${metrics.lastToggleTime.toFixed(2)}ms`);
    console.log('Performance Status:', this.isPerformanceGood() ? '✅ Good' : '⚠️ Needs Attention');
    
    if (warnings.length > 0) {
      console.warn('Performance Warnings:');
      warnings.forEach(warning => console.warn(`- ${warning}`));
    }
    
    console.groupEnd();
  }
}

// Singleton instance
export const sidebarPerformanceMonitor = new SidebarPerformanceMonitor();

/**
 * Debounce utility for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null;
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout);
    }
    
    timeout = window.setTimeout(() => {
      func(...args);
    }, wait);
  };
}

/**
 * Throttle utility for performance optimization
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * RAF (RequestAnimationFrame) utility for smooth animations
 */
export function rafScheduler(callback: () => void): void {
  if (browser && window.requestAnimationFrame) {
    window.requestAnimationFrame(callback);
  } else {
    setTimeout(callback, 16); // Fallback to ~60fps
  }
}

/**
 * Check if device supports hardware acceleration
 */
export function supportsHardwareAcceleration(): boolean {
  if (!browser) return false;
  
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  return !!gl;
}

/**
 * Optimize CSS transitions based on device capabilities
 */
export function getOptimalTransitionDuration(): number {
  if (!browser) return 300;
  
  // Reduce animation duration on slower devices
  const isSlowDevice = !supportsHardwareAcceleration() || 
                      navigator.hardwareConcurrency < 4 ||
                      (navigator as any).deviceMemory < 4;
  
  return isSlowDevice ? 200 : 300;
}

/**
 * Memory usage monitoring (if available)
 */
export function getMemoryUsage(): { used: number; total: number } | null {
  if (!browser || !(performance as any).memory) return null;
  
  const memory = (performance as any).memory;
  return {
    used: memory.usedJSHeapSize,
    total: memory.totalJSHeapSize,
  };
}

/**
 * Check if user prefers reduced motion
 */
export function prefersReducedMotion(): boolean {
  if (!browser) return false;
  
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * Performance-aware CSS class generator
 */
export function getPerformanceOptimizedClasses(): string[] {
  const classes: string[] = [];
  
  if (prefersReducedMotion()) {
    classes.push('motion-reduce:transition-none');
  }
  
  if (!supportsHardwareAcceleration()) {
    classes.push('transform-gpu-off');
  }
  
  return classes;
}

// Development mode performance logging
if (browser && import.meta.env.DEV) {
  // Log performance summary every 30 seconds in development
  setInterval(() => {
    const warnings = sidebarPerformanceMonitor.getPerformanceWarnings();
    if (warnings.length > 0) {
      sidebarPerformanceMonitor.logPerformanceSummary();
    }
  }, 30000);
}
