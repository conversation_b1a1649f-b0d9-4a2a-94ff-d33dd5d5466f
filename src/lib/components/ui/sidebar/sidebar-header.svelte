<script lang="ts">
	import type { HTMLAttributes } from "svelte/elements";
	import { cn, type WithElementRef } from "$lib/utils.js";

	let {
		ref = $bindable(null),
		class: className,
		children,
		...restProps
	}: WithElementRef<HTMLAttributes<HTMLElement>> = $props();
</script>

<div
	bind:this={ref}
	data-slot="sidebar-header"
	data-sidebar="header"
	class={cn(
		"flex flex-col gap-2 p-2 border-b border-sidebar-border/50",
		"group-data-[collapsible=icon]:p-2 group-data-[collapsible=icon]:justify-center",
		"transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)] motion-reduce:transition-none",
		className
	)}
	{...restProps}
>
	{@render children?.()}
</div>
