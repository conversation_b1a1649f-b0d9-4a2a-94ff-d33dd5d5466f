import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/svelte';
import { userEvent } from '@testing-library/user-event';
import SidebarProvider from './sidebar-provider.svelte';
import Sidebar from './sidebar.svelte';
import SidebarTrigger from './sidebar-trigger.svelte';
import SidebarContent from './sidebar-content.svelte';
import SidebarHeader from './sidebar-header.svelte';
import SidebarFooter from './sidebar-footer.svelte';

// Mock browser environment
Object.defineProperty(window, 'innerWidth', {
  writable: true,
  configurable: true,
  value: 1024,
});

Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Test component that includes all sidebar parts
const TestSidebar = `
<script>
  import SidebarProvider from './sidebar-provider.svelte';
  import Sidebar from './sidebar.svelte';
  import SidebarTrigger from './sidebar-trigger.svelte';
  import SidebarContent from './sidebar-content.svelte';
  import SidebarHeader from './sidebar-header.svelte';
  import SidebarFooter from './sidebar-footer.svelte';
  
  let open = true;
</script>

<SidebarProvider bind:open>
  <Sidebar>
    <SidebarHeader>
      <h2>Header</h2>
    </SidebarHeader>
    <SidebarContent>
      <p>Content</p>
    </SidebarContent>
    <SidebarFooter>
      <p>Footer</p>
    </SidebarFooter>
  </Sidebar>
  <main>
    <SidebarTrigger />
    <p>Main content</p>
  </main>
</SidebarProvider>
`;

describe('Sidebar Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('SidebarProvider', () => {
    it('should render children correctly', () => {
      const { container } = render(SidebarProvider, {
        props: {
          children: () => 'Test content'
        }
      });
      
      expect(container.textContent).toContain('Test content');
    });

    it('should provide sidebar context', () => {
      const { container } = render(SidebarProvider, {
        props: {
          open: true,
          children: () => 'Test content'
        }
      });
      
      expect(container.querySelector('[data-slot="sidebar-wrapper"]')).toBeInTheDocument();
    });

    it('should handle open state changes', async () => {
      let openState = true;
      const onOpenChange = vi.fn((value) => {
        openState = value;
      });

      render(SidebarProvider, {
        props: {
          open: openState,
          onOpenChange,
          children: () => 'Test content'
        }
      });

      // Test would need to trigger state change through child components
      expect(onOpenChange).toBeDefined();
    });
  });

  describe('Sidebar', () => {
    it('should render with correct attributes', () => {
      const { container } = render(Sidebar, {
        props: {
          children: () => 'Sidebar content'
        },
        context: new Map([
          ['sidebar', {
            state: 'expanded',
            isMobile: false,
            open: true,
            openMobile: false,
            setOpen: vi.fn(),
            setOpenMobile: vi.fn(),
            toggle: vi.fn(),
            announceStateChange: vi.fn()
          }]
        ])
      });

      const sidebar = container.querySelector('[data-slot="sidebar"]');
      expect(sidebar).toBeInTheDocument();
      expect(sidebar).toHaveAttribute('role', 'complementary');
      expect(sidebar).toHaveAttribute('aria-label', 'Navigation sidebar');
    });

    it('should handle different variants', () => {
      const { container } = render(Sidebar, {
        props: {
          variant: 'floating',
          children: () => 'Sidebar content'
        },
        context: new Map([
          ['sidebar', {
            state: 'expanded',
            isMobile: false,
            open: true,
            openMobile: false,
            setOpen: vi.fn(),
            setOpenMobile: vi.fn(),
            toggle: vi.fn(),
            announceStateChange: vi.fn()
          }]
        ])
      });

      const sidebar = container.querySelector('[data-slot="sidebar"]');
      expect(sidebar).toHaveAttribute('data-variant', 'floating');
    });

    it('should handle collapsed state', () => {
      const { container } = render(Sidebar, {
        props: {
          collapsible: 'icon',
          children: () => 'Sidebar content'
        },
        context: new Map([
          ['sidebar', {
            state: 'collapsed',
            isMobile: false,
            open: false,
            openMobile: false,
            setOpen: vi.fn(),
            setOpenMobile: vi.fn(),
            toggle: vi.fn(),
            announceStateChange: vi.fn()
          }]
        ])
      });

      const sidebar = container.querySelector('[data-slot="sidebar"]');
      expect(sidebar).toHaveAttribute('data-state', 'collapsed');
      expect(sidebar).toHaveAttribute('data-collapsible', 'icon');
    });
  });

  describe('SidebarTrigger', () => {
    it('should render trigger button', () => {
      const mockToggle = vi.fn();
      
      render(SidebarTrigger, {
        context: new Map([
          ['sidebar', {
            state: 'expanded',
            toggle: mockToggle,
            announceStateChange: vi.fn()
          }]
        ])
      });

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-expanded', 'true');
      expect(button).toHaveAttribute('aria-controls', 'sidebar-content');
    });

    it('should call toggle when clicked', async () => {
      const user = userEvent.setup();
      const mockToggle = vi.fn();
      
      render(SidebarTrigger, {
        context: new Map([
          ['sidebar', {
            state: 'expanded',
            toggle: mockToggle,
            announceStateChange: vi.fn()
          }]
        ])
      });

      const button = screen.getByRole('button');
      await user.click(button);
      
      expect(mockToggle).toHaveBeenCalledOnce();
    });

    it('should show correct aria-label for collapsed state', () => {
      render(SidebarTrigger, {
        context: new Map([
          ['sidebar', {
            state: 'collapsed',
            toggle: vi.fn(),
            announceStateChange: vi.fn()
          }]
        ])
      });

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Expand sidebar');
      expect(button).toHaveAttribute('aria-expanded', 'false');
    });
  });

  describe('SidebarContent', () => {
    it('should render content with correct attributes', () => {
      const { container } = render(SidebarContent, {
        props: {
          children: () => 'Content'
        }
      });

      const content = container.querySelector('[data-slot="sidebar-content"]');
      expect(content).toBeInTheDocument();
      expect(content).toHaveAttribute('data-sidebar', 'content');
    });

    it('should handle overflow correctly', () => {
      const { container } = render(SidebarContent, {
        props: {
          children: () => 'Content'
        }
      });

      const content = container.querySelector('[data-slot="sidebar-content"]');
      expect(content).toHaveClass('overflow-auto');
    });
  });

  describe('SidebarHeader', () => {
    it('should render header with border', () => {
      const { container } = render(SidebarHeader, {
        props: {
          children: () => 'Header'
        }
      });

      const header = container.querySelector('[data-slot="sidebar-header"]');
      expect(header).toBeInTheDocument();
      expect(header).toHaveClass('border-b');
    });
  });

  describe('SidebarFooter', () => {
    it('should render footer with border', () => {
      const { container } = render(SidebarFooter, {
        props: {
          children: () => 'Footer'
        }
      });

      const footer = container.querySelector('[data-slot="sidebar-footer"]');
      expect(footer).toBeInTheDocument();
      expect(footer).toHaveClass('border-t');
      expect(footer).toHaveClass('mt-auto');
    });
  });
});

describe('Keyboard Navigation', () => {
  it('should handle keyboard shortcuts', async () => {
    const user = userEvent.setup();
    const mockToggle = vi.fn();
    
    render(SidebarProvider, {
      props: {
        open: true,
        children: () => 'Test content'
      }
    });

    // Simulate Ctrl+B
    await user.keyboard('{Control>}b{/Control}');
    
    // Note: This test would need proper context setup to work fully
    // The actual keyboard handling is in the SidebarState class
  });
});

describe('Mobile Behavior', () => {
  beforeEach(() => {
    // Mock mobile environment
    Object.defineProperty(window, 'innerWidth', { value: 600 });
    vi.mocked(window.matchMedia).mockReturnValue({
      matches: true,
      media: '',
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    });
  });

  it('should render mobile sheet on mobile devices', () => {
    const { container } = render(Sidebar, {
      props: {
        children: () => 'Sidebar content'
      },
      context: new Map([
        ['sidebar', {
          state: 'expanded',
          isMobile: true,
          open: true,
          openMobile: true,
          setOpen: vi.fn(),
          setOpenMobile: vi.fn(),
          toggle: vi.fn(),
          announceStateChange: vi.fn()
        }]
      ])
    });

    // Should render mobile sheet instead of desktop sidebar
    expect(container.querySelector('[data-mobile="true"]')).toBeInTheDocument();
  });
});
