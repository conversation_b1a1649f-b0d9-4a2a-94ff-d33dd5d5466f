# Enhanced Sidebar Component

A professional, accessible, and performant sidebar component for SvelteKit applications with comprehensive state management, smooth animations, and responsive design.

## Features

### ✨ Core Functionality
- **Collapsible Design**: Supports icon-only and off-canvas collapse modes
- **State Persistence**: Remembers user preferences across sessions via cookies
- **Responsive Behavior**: Adapts seamlessly between desktop and mobile layouts
- **Keyboard Navigation**: Full keyboard support with customizable shortcuts

### 🎨 Visual Design
- **Smooth Animations**: CSS-based transitions with hardware acceleration
- **Multiple Variants**: Standard, floating, and inset layout options
- **Consistent Spacing**: Carefully designed typography and spacing system
- **Content Overflow**: Proper handling of content in both expanded and collapsed states

### ♿ Accessibility
- **ARIA Support**: Complete ARIA labels and attributes
- **Screen Reader**: Announcements for state changes
- **Focus Management**: Proper focus handling during state transitions
- **Keyboard Shortcuts**: Ctrl/Cmd+B to toggle, Escape to close

### ⚡ Performance
- **Optimized Rendering**: Minimal re-renders with efficient state management
- **Hardware Acceleration**: GPU-accelerated animations when available
- **Mobile Optimization**: Reduced complexity on mobile devices
- **Memory Management**: Proper cleanup of event listeners and timers

## Usage

### Basic Setup

```svelte
<script>
  import {
    SidebarProvider,
    Sidebar,
    SidebarContent,
    SidebarHeader,
    SidebarFooter,
    SidebarTrigger
  } from '$lib/components/ui/sidebar';
  
  let sidebarOpen = false;
</script>

<SidebarProvider bind:open={sidebarOpen}>
  <Sidebar>
    <SidebarHeader>
      <h2>Navigation</h2>
    </SidebarHeader>
    
    <SidebarContent>
      <!-- Your navigation content -->
    </SidebarContent>
    
    <SidebarFooter>
      <!-- Footer content -->
    </SidebarFooter>
  </Sidebar>
  
  <main>
    <SidebarTrigger />
    <!-- Your main content -->
  </main>
</SidebarProvider>
```

### Advanced Configuration

```svelte
<SidebarProvider 
  bind:open={sidebarOpen}
  onOpenChange={handleSidebarChange}
>
  <Sidebar 
    collapsible="icon"
    variant="floating"
    side="left"
  >
    <!-- Sidebar content -->
  </Sidebar>
</SidebarProvider>
```

## Components

### SidebarProvider
The root component that provides sidebar context to all child components.

**Props:**
- `open: boolean` - Controls sidebar open/closed state
- `onOpenChange: (open: boolean) => void` - Callback when state changes

### Sidebar
The main sidebar container component.

**Props:**
- `collapsible: "offcanvas" | "icon" | "none"` - Collapse behavior
- `variant: "sidebar" | "floating" | "inset"` - Visual variant
- `side: "left" | "right"` - Sidebar position

### SidebarTrigger
Button component to toggle sidebar state.

**Features:**
- Automatic ARIA attributes
- Visual state indicators
- Keyboard accessible

### SidebarContent
Scrollable content area of the sidebar.

**Features:**
- Custom scrollbar styling
- Overflow handling
- Smooth transitions

## Keyboard Shortcuts

- **Ctrl/Cmd + B**: Toggle sidebar
- **Escape**: Close sidebar (when expanded)
- **Tab**: Navigate through sidebar items
- **Enter/Space**: Activate sidebar items

## State Management

The sidebar uses a sophisticated state management system:

```typescript
interface SidebarState {
  open: boolean;           // Desktop sidebar state
  openMobile: boolean;     // Mobile sidebar state
  isMobile: boolean;       // Mobile detection
  state: "expanded" | "collapsed"; // Computed state
  toggle(): void;          // Toggle function
  announceStateChange(): void; // Accessibility announcements
}
```

## Responsive Behavior

### Desktop (≥768px)
- Fixed positioned sidebar
- Smooth width transitions
- Icon-only collapse mode available

### Mobile (<768px)
- Sheet/overlay presentation
- Touch-friendly interactions
- Swipe gestures (if implemented)

## Accessibility Features

### ARIA Attributes
- `role="complementary"` on sidebar
- `aria-expanded` on trigger button
- `aria-controls` linking trigger to content
- `aria-current="page"` on active navigation items

### Screen Reader Support
- State change announcements
- Descriptive labels
- Proper heading hierarchy

### Keyboard Navigation
- Full keyboard accessibility
- Logical tab order
- Visible focus indicators

## Performance Optimizations

### Rendering
- Minimal re-renders with derived state
- Efficient mobile detection
- Debounced resize handling

### Animations
- Hardware-accelerated CSS transitions
- Reduced motion support
- Performance monitoring in development

### Memory Management
- Proper event listener cleanup
- Timeout management
- Resource disposal on unmount

## Testing

The sidebar includes comprehensive test coverage:

### Unit Tests
```bash
npm run test src/lib/components/ui/sidebar/sidebar.test.ts
```

### Component Tests
```bash
npm run test src/lib/components/ui/sidebar/sidebar.svelte.test.ts
```

### E2E Tests
```bash
npm run test:e2e e2e/sidebar.test.ts
```

## Customization

### CSS Variables
```css
:root {
  --sidebar-width: 16rem;
  --sidebar-width-icon: 3rem;
  --sidebar-width-mobile: 18rem;
}
```

### Custom Animations
```css
.custom-sidebar-transition {
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
}
```

## Browser Support

- **Modern Browsers**: Full feature support
- **Legacy Browsers**: Graceful degradation
- **Mobile Browsers**: Optimized experience
- **Screen Readers**: Full accessibility support

## Performance Monitoring

In development mode, the sidebar includes performance monitoring:

```javascript
import { sidebarPerformanceMonitor } from '$lib/components/ui/sidebar/performance';

// View performance metrics
console.log(sidebarPerformanceMonitor.getMetrics());

// Check performance status
console.log(sidebarPerformanceMonitor.isPerformanceGood());
```

## Migration Guide

If upgrading from a previous sidebar implementation:

1. Update imports to use new component structure
2. Replace old state management with SidebarProvider
3. Update CSS classes to use new design system
4. Test accessibility features
5. Verify performance in your specific use case

## Contributing

When contributing to the sidebar component:

1. Run all tests before submitting
2. Ensure accessibility compliance
3. Test on multiple devices and browsers
4. Update documentation for new features
5. Follow the established code style

## License

This component is part of the SourceFlex UI library and follows the project's licensing terms.
