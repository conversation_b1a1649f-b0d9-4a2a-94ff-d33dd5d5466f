<script lang="ts">
	import type { HTMLAttributes } from "svelte/elements";
	import { cn, type WithElementRef } from "$lib/utils.js";

	let {
		ref = $bindable(null),
		class: className,
		children,
		...restProps
	}: WithElementRef<HTMLAttributes<HTMLElement>> = $props();
</script>

<div
	bind:this={ref}
	data-slot="sidebar-content"
	data-sidebar="content"
	class={cn(
		"flex min-h-0 flex-1 flex-col gap-2 overflow-auto transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]",
		"motion-reduce:transition-none",
		"scrollbar-thin scrollbar-track-transparent scrollbar-thumb-sidebar-border hover:scrollbar-thumb-sidebar-accent",
		"group-data-[collapsible=icon]:overflow-hidden group-data-[collapsible=icon]:opacity-0 group-data-[collapsible=icon]:pointer-events-none",
		"group-data-[state=expanded]:opacity-100 group-data-[state=expanded]:pointer-events-auto",
		"px-2 py-2", // Better padding for content
		className
	)}
	{...restProps}
>
	{@render children?.()}
</div>
