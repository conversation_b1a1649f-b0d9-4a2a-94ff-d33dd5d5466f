<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { cn } from '$lib/utils';

	import { getSidebar } from './context.svelte.js';

	type $$Props = {
		class?: string;
	};

	let { class: className, ...restProps }: $$Props = $props();

	const sidebar = getSidebar();
	const { toggle, state } = sidebar;
</script>

<Button
	variant="ghost"
	size="icon"
	class={cn('h-7 w-7 transition-transform duration-200 ease-[cubic-bezier(0.4,0,0.2,1)] hover:scale-105 active:scale-95 motion-reduce:transition-none motion-reduce:hover:scale-100 motion-reduce:active:scale-100', className)}
	onclick={toggle}
	aria-label={sidebar.state === 'expanded' ? 'Collapse sidebar' : 'Expand sidebar'}
	aria-expanded={sidebar.state === 'expanded'}
	aria-controls="sidebar-content"
	title={sidebar.state === 'expanded' ? 'Collapse sidebar (Ctrl+B)' : 'Expand sidebar (Ctrl+B)'}
	{...restProps}
>
	<svg
		width="16"
		height="16"
		viewBox="0 0 24 24"
		fill="none"
		stroke="currentColor"
		stroke-width="2"
		class="transition-transform duration-200 ease-[cubic-bezier(0.4,0,0.2,1)] motion-reduce:transition-none"
		aria-hidden="true"
	>
		{#if sidebar.state === 'expanded'}
			<!-- Collapse icon -->
			<path d="M3 12h18m-9-9l-9 9 9 9"/>
		{:else}
			<!-- Expand icon -->
			<path d="M21 12H3m18-9l-9 9-9-9"/>
		{/if}
	</svg>
	<span class="sr-only">{sidebar.state === 'expanded' ? 'Collapse sidebar' : 'Expand sidebar'}</span>
</Button>
