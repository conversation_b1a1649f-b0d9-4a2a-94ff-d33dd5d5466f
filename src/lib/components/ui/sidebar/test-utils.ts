import { vi } from 'vitest';
import type { SidebarState } from './context.svelte.js';

/**
 * Mock sidebar context for testing
 */
export function createMockSidebarContext(overrides: Partial<SidebarState> = {}) {
  return {
    open: true,
    openMobile: false,
    isMobile: false,
    state: 'expanded' as const,
    setOpen: vi.fn(),
    setOpenMobile: vi.fn(),
    toggle: vi.fn(),
    announceStateChange: vi.fn(),
    handleShortcutKeydown: vi.fn(),
    destroy: vi.fn(),
    ...overrides,
  };
}

/**
 * Mock browser environment for testing
 */
export function setupBrowserMocks() {
  // Mock window.innerWidth
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: 1024,
  });

  // Mock window.matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  // Mock document.cookie
  Object.defineProperty(document, 'cookie', {
    writable: true,
    value: '',
  });

  // Mock document.createElement and appendChild for accessibility announcements
  const mockElement = {
    setAttribute: vi.fn(),
    textContent: '',
    className: '',
  };

  vi.spyOn(document, 'createElement').mockReturnValue(mockElement as any);
  vi.spyOn(document.body, 'appendChild').mockImplementation(() => mockElement as any);
  vi.spyOn(document.body, 'removeChild').mockImplementation(() => mockElement as any);
}

/**
 * Mock mobile environment
 */
export function setupMobileMocks() {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: 600,
  });

  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: true,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
}

/**
 * Mock desktop environment
 */
export function setupDesktopMocks() {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: 1024,
  });

  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
}

/**
 * Create a keyboard event for testing
 */
export function createKeyboardEvent(key: string, options: Partial<KeyboardEventInit> = {}) {
  return new KeyboardEvent('keydown', {
    key,
    bubbles: true,
    cancelable: true,
    ...options,
  });
}

/**
 * Wait for animations to complete
 */
export function waitForAnimation(duration = 300) {
  return new Promise(resolve => setTimeout(resolve, duration));
}

/**
 * Test helper to verify accessibility attributes
 */
export function expectAccessibilityAttributes(element: Element, expectedAttributes: Record<string, string>) {
  Object.entries(expectedAttributes).forEach(([attr, value]) => {
    expect(element).toHaveAttribute(attr, value);
  });
}

/**
 * Test helper to verify CSS classes
 */
export function expectCSSClasses(element: Element, expectedClasses: string[]) {
  expectedClasses.forEach(className => {
    expect(element).toHaveClass(className);
  });
}

/**
 * Mock ResizeObserver for testing
 */
export function mockResizeObserver() {
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));
}

/**
 * Mock IntersectionObserver for testing
 */
export function mockIntersectionObserver() {
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));
}

/**
 * Clean up all mocks
 */
export function cleanupMocks() {
  vi.clearAllMocks();
  vi.restoreAllMocks();
}

/**
 * Test data for sidebar components
 */
export const testData = {
  navigationItems: [
    { title: 'Dashboard', url: '/dashboard', icon: 'LayoutDashboard' },
    { title: 'Projects', url: '/projects', icon: 'Briefcase' },
    { title: 'Team', url: '/team', icon: 'Users' },
    { title: 'Goals', url: '/goals', icon: 'Target' },
    { title: 'Reports', url: '/reports', icon: 'BarChart3' },
    { title: 'Settings', url: '/settings', icon: 'Settings' },
  ],
  user: {
    name: 'Test User',
    email: '<EMAIL>',
    initials: 'TU',
  },
};

/**
 * Custom render function with sidebar context
 */
export function renderWithSidebarContext(
  component: any,
  props: any = {},
  contextOverrides: Partial<SidebarState> = {}
) {
  const mockContext = createMockSidebarContext(contextOverrides);
  
  return {
    component,
    props,
    context: new Map([['sidebar', mockContext]]),
  };
}
