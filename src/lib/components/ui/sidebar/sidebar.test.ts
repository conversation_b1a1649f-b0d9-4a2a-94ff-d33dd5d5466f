import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SidebarState } from './context.svelte.js';
import { SIDEBAR_COOKIE_NAME, SIDEBAR_MOBILE_BREAKPOINT } from './constants.js';

// Mock browser environment
const mockWindow = {
  innerWidth: 1024,
  matchMedia: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  setTimeout: vi.fn(),
  clearTimeout: vi.fn(),
  requestAnimationFrame: vi.fn(),
};

const mockDocument = {
  cookie: '',
  createElement: vi.fn().mockReturnValue({
    setAttribute: vi.fn(),
    textContent: '',
    className: '',
  }),
  body: {
    appendChild: vi.fn(),
    removeChild: vi.fn(),
  },
};

// Mock KeyboardEvent
class MockKeyboardEvent {
  key: string;
  ctrlKey: boolean;
  metaKey: boolean;
  preventDefault: () => void;

  constructor(type: string, options: any = {}) {
    this.key = options.key || '';
    this.ctrlKey = options.ctrlKey || false;
    this.metaKey = options.metaKey || false;
    this.preventDefault = vi.fn();
  }
}

// Set up global mocks
vi.stubGlobal('window', mockWindow);
vi.stubGlobal('document', mockDocument);
vi.stubGlobal('KeyboardEvent', MockKeyboardEvent);

describe('SidebarState', () => {
  let sidebarState: SidebarState;
  let mockSetOpen: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockSetOpen = vi.fn();
    sidebarState = new SidebarState({
      open: () => true,
      setOpen: mockSetOpen,
    });
  });

  afterEach(() => {
    sidebarState.destroy();
    vi.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize with correct default state', () => {
      expect(sidebarState.open).toBe(true);
      expect(sidebarState.state).toBe('expanded');
      expect(sidebarState.openMobile).toBe(false);
    });

    it('should initialize mobile detector with correct breakpoint', () => {
      expect(sidebarState.isMobile).toBeDefined();
    });
  });

  describe('state management', () => {
    it('should toggle desktop sidebar state', () => {
      // Mock desktop environment
      vi.mocked(window.matchMedia).mockReturnValue({
        matches: false,
        media: '',
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      });

      sidebarState.toggle();
      expect(mockSetOpen).toHaveBeenCalledWith(false);
    });

    it('should toggle mobile sidebar state', () => {
      // Test that mobile state can be toggled
      const initialMobileState = sidebarState.openMobile;
      sidebarState.setOpenMobile(!initialMobileState);
      expect(sidebarState.openMobile).toBe(!initialMobileState);
    });
  });

  describe('keyboard shortcuts', () => {
    it('should have keyboard shortcut handler', () => {
      expect(typeof sidebarState.handleShortcutKeydown).toBe('function');
    });

    it('should handle keyboard events without errors', () => {
      const event = new (KeyboardEvent as any)('keydown', {
        key: 'b',
        ctrlKey: true,
      });

      expect(() => sidebarState.handleShortcutKeydown(event)).not.toThrow();
    });

    it('should handle different key combinations', () => {
      const events = [
        new (KeyboardEvent as any)('keydown', { key: 'b', ctrlKey: true }),
        new (KeyboardEvent as any)('keydown', { key: 'b', metaKey: true }),
        new (KeyboardEvent as any)('keydown', { key: 'Escape' }),
        new (KeyboardEvent as any)('keydown', { key: 'a', ctrlKey: true }),
      ];

      events.forEach(event => {
        expect(() => sidebarState.handleShortcutKeydown(event)).not.toThrow();
      });
    });
  });

  describe('cookie management', () => {
    beforeEach(() => {
      mockDocument.cookie = '';
    });

    it('should have cookie management methods', () => {
      expect(typeof SidebarState.readStateFromCookie).toBe('function');
      expect(typeof SidebarState.saveStateToCookie).toBe('function');
    });

    it('should handle cookie operations without errors', () => {
      expect(() => SidebarState.saveStateToCookie(true)).not.toThrow();
      expect(() => SidebarState.readStateFromCookie()).not.toThrow();
    });

    it('should return boolean from readStateFromCookie', () => {
      const result = SidebarState.readStateFromCookie();
      expect(typeof result).toBe('boolean');
    });
  });

  describe('accessibility', () => {
    it('should have announce state change method', () => {
      // Test that the method exists and can be called
      expect(typeof sidebarState.announceStateChange).toBe('function');
      expect(() => sidebarState.announceStateChange()).not.toThrow();
    });
  });

  describe('cleanup', () => {
    it('should clean up resources on destroy', () => {
      // Test that destroy method exists and can be called
      expect(() => sidebarState.destroy()).not.toThrow();
    });
  });
});

describe('Mobile Detection', () => {
  it('should detect mobile correctly based on breakpoint', () => {
    mockWindow.innerWidth = 600;

    const sidebarState = new SidebarState({
      open: () => true,
      setOpen: vi.fn(),
    });

    // Mobile detection is available
    expect(sidebarState.isMobile).toBeDefined();
    expect(typeof sidebarState.isMobile).toBe('boolean');

    sidebarState.destroy();
  });
});
