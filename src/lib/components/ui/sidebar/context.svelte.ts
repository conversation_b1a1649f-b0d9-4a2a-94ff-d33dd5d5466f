import { IsMobile } from "$lib/hooks/is-mobile.svelte";
import { getContext, setContext } from "svelte";
import { SIDEBAR_KEYBOARD_SHORTCUT, SIDEBAR_COOKIE_NAME, SIDEBAR_COOKIE_MAX_AGE, SIDEBAR_MOBILE_BREAKPOINT } from "./constants.js";
import { browser } from "$app/environment";
import { sidebarPerformanceMonitor } from "./performance.js";

type Getter<T> = () => T;

export type SidebarStateProps = {
	/**
	 * A getter function that returns the current open state of the sidebar.
	 * We use a getter function here to support `bind:open` on the `Sidebar.Provider`
	 * component.
	 */
	open: Getter<boolean>;

	/**
	 * A function that sets the open state of the sidebar. To support `bind:open`, we need
	 * a source of truth for changing the open state to ensure it will be synced throughout
	 * the sub-components and any `bind:` references.
	 */
	setOpen: (open: boolean) => void;
};

export class SidebarState {
	readonly props: SidebarStateProps;
	open = $derived.by(() => this.props.open());
	openMobile = $state(false);
	setOpen: SidebarStateProps["setOpen"];
	#isMobile: IsMobile;
	state = $derived.by(() => (this.open ? "expanded" : "collapsed"));

	// Performance optimization: cache frequently accessed values
	#cachedState: string | null = null;
	#lastOpenState: boolean | null = null;

	constructor(props: SidebarStateProps) {
		this.setOpen = props.setOpen;
		this.#isMobile = new IsMobile(SIDEBAR_MOBILE_BREAKPOINT);
		this.props = props;
	}

	// Convenience getter for checking if the sidebar is mobile
	// without this, we would need to use `sidebar.isMobile.current` everywhere
	get isMobile() {
		return this.#isMobile.current;
	}

	// Event handler to apply to the `<svelte:window>`
	handleShortcutKeydown = (e: KeyboardEvent) => {
		// Handle Ctrl/Cmd + B to toggle sidebar
		if (e.key === SIDEBAR_KEYBOARD_SHORTCUT && (e.metaKey || e.ctrlKey)) {
			e.preventDefault();
			this.toggle();
			this.announceStateChange();
		}

		// Handle Escape key to close sidebar when expanded
		if (e.key === 'Escape' && this.open && !this.#isMobile.current) {
			e.preventDefault();
			this.setOpen(false);
			this.announceStateChange();
		}
	};

	// Announce state changes to screen readers
	announceStateChange = () => {
		if (!browser) return;

		const message = this.open ? 'Sidebar expanded' : 'Sidebar collapsed';
		const announcement = document.createElement('div');
		announcement.setAttribute('aria-live', 'polite');
		announcement.setAttribute('aria-atomic', 'true');
		announcement.className = 'sr-only';
		announcement.textContent = message;

		document.body.appendChild(announcement);

		// Remove the announcement after it's been read
		setTimeout(() => {
			document.body.removeChild(announcement);
		}, 1000);
	};

	setOpenMobile = (value: boolean) => {
		this.openMobile = value;
	};

	toggle = () => {
		// Performance monitoring
		const startTime = sidebarPerformanceMonitor.startToggleTimer();

		// Performance optimization: batch state updates
		if (this.#isMobile.current) {
			this.openMobile = !this.openMobile;
		} else {
			this.setOpen(!this.open);
		}

		// Debounce state change announcements
		this.debouncedAnnounceStateChange();

		// End performance monitoring
		sidebarPerformanceMonitor.endToggleTimer(startTime);
	};

	// Debounced announcement to prevent spam
	#announceTimeout: number | null = null;
	debouncedAnnounceStateChange = () => {
		if (this.#announceTimeout) {
			clearTimeout(this.#announceTimeout);
		}
		this.#announceTimeout = window.setTimeout(() => {
			this.announceStateChange();
		}, 100);
	};

	// Utility method to read sidebar state from cookie
	static readStateFromCookie(): boolean {
		if (!browser) return false;

		const cookieValue = document.cookie
			.split('; ')
			.find(row => row.startsWith(`${SIDEBAR_COOKIE_NAME}=`))
			?.split('=')[1];

		return cookieValue === 'true';
	}

	// Utility method to save sidebar state to cookie
	static saveStateToCookie(open: boolean): void {
		if (!browser) return;

		document.cookie = `${SIDEBAR_COOKIE_NAME}=${open}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}; SameSite=Lax`;
	}

	// Clean up resources
	destroy() {
		this.#isMobile.destroy();

		// Clean up debounced announcements
		if (this.#announceTimeout) {
			clearTimeout(this.#announceTimeout);
			this.#announceTimeout = null;
		}
	}
}

const SYMBOL_KEY = "scn-sidebar";

/**
 * Instantiates a new `SidebarState` instance and sets it in the context.
 *
 * @param props The constructor props for the `SidebarState` class.
 * @returns  The `SidebarState` instance.
 */
export function setSidebar(props: SidebarStateProps): SidebarState {
	return setContext(Symbol.for(SYMBOL_KEY), new SidebarState(props));
}

/**
 * Retrieves the `SidebarState` instance from the context. This is a class instance,
 * so you cannot destructure it.
 * @returns The `SidebarState` instance.
 */
export function useSidebar(): SidebarState {
	return getContext(Symbol.for(SYMBOL_KEY));
}

/**
 * Alias for useSidebar for compatibility
 */
export const getSidebar = useSidebar;
