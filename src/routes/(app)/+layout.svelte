<script lang="ts">
	import type { LayoutData } from './$types';
	import type { Snippet } from 'svelte';
	import {
		Sidebar,
		SidebarContent,
		SidebarFooter,
		SidebarGroup,
		SidebarGroupContent,
		SidebarGroupLabel,
		SidebarHeader,
		SidebarInset,
		SidebarMenu,
		SidebarMenuButton,
		SidebarMenuItem,
		SidebarProvider,
		SidebarTrigger
	} from '$lib/components/ui/sidebar';
	import { Button } from '$lib/components/ui/button';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { 
		LayoutDashboard,
		Briefcase,
		Users,
		Target,
		BarChart3,
		Settings,
		LogOut,
		ChevronUp,
		MoreHorizontal,
		User,
		ChevronsUpDown
	} from 'lucide-svelte';
	import { browser } from '$app/environment';
	import type { ComponentType } from 'svelte';
	import type { Icon } from 'lucide-svelte';
	import { SidebarState } from '$lib/components/ui/sidebar/context.svelte.js';

	let { data, children }: { data: LayoutData; children: Snippet } = $props();

	// Initialize sidebar state from cookie or default to collapsed
	let sidebarOpen = $state(browser ? SidebarState.readStateFromCookie() : false);

	// Get user initials for avatar
	function getUserInitials(user: any): string {
		if (user?.name) {
			return user.name.split(' ').map((n: string) => n[0]).join('').substring(0, 2).toUpperCase();
		}
		if (user?.primaryEmail) {
			return user.primaryEmail.substring(0, 2).toUpperCase();
		}
		return 'U';
	}

	function handleSignOut() {
		window.location.href = '/signout';
	}

	// Navigation items with proper Lucide icons
	type NavigationItem = {
		title: string;
		url: string;
		icon: ComponentType<Icon>;
	};

	const navigationItems: NavigationItem[] = [
		{
			title: 'Dashboard',
			url: '/dashboard',
			icon: LayoutDashboard
		},
		{
			title: 'Jobs',
			url: '/jobs',
			icon: Briefcase
		},
		{
			title: 'Candidates',
			url: '/candidates',
			icon: Users
		},
		{
			title: 'Interviews',
			url: '/interviews',
			icon: Target
		},
		{
			title: 'Reports',
			url: '/reports',
			icon: BarChart3
		},
		{
			title: 'Settings',
			url: '/settings',
			icon: Settings
		}
	];
</script>

<SidebarProvider bind:open={sidebarOpen}>
	<Sidebar collapsible="icon" class="border-r border-sidebar-border">
		<SidebarHeader class="border-b border-sidebar-border/50 pb-4">
			<SidebarMenu>
				<SidebarMenuItem>
					<SidebarMenuButton size="lg" class="hover:bg-sidebar-accent/50 transition-all duration-200">
						{#snippet child({ props })}
							<a href="/dashboard" class="group" {...props}>
								<div class="flex aspect-square size-10 items-center justify-center rounded-xl bg-gradient-to-br from-primary to-primary/80 text-primary-foreground shadow-lg group-hover:shadow-xl transition-all duration-200 group-hover:scale-105">
									<span class="text-base font-bold tracking-tight">SF</span>
								</div>
								<div class="grid flex-1 text-left leading-tight ml-1">
									<span class="truncate font-bold text-base text-sidebar-foreground">SourceFlex</span>
									<span class="truncate text-xs text-sidebar-foreground/60 font-medium">Talent Management</span>
								</div>
							</a>
						{/snippet}
					</SidebarMenuButton>
				</SidebarMenuItem>
			</SidebarMenu>
		</SidebarHeader>

		<SidebarContent class="px-2 py-4">
			<SidebarGroup>
				<SidebarGroupLabel class="px-3 py-2 text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider">
					Navigation
				</SidebarGroupLabel>
				<SidebarGroupContent class="mt-2">
					<SidebarMenu class="space-y-1">
						{#each navigationItems as item}
							{@const Icon = item.icon}
							{@const isActive = typeof window !== 'undefined' && window.location.pathname === item.url}
							<SidebarMenuItem class="sidebar-menu-item" data-active={isActive}>
								<div class="sidebar-active-indicator"></div>
								<SidebarMenuButton 
									isActive={isActive}
									class="group relative h-11 px-3 rounded-lg transition-all duration-200 hover:bg-sidebar-accent/80 hover:scale-[1.02] active:scale-[0.98] {isActive ? 'bg-sidebar-accent text-sidebar-accent-foreground shadow-sm font-medium' : ''}"
									tooltipContent={item.title}
								>
									{#snippet child({ props })}
										<a href={item.url} class="flex items-center gap-3 w-full" {...props}>
											<div class="flex items-center justify-center size-5 transition-transform duration-200 group-hover:scale-110">
												<Icon class="size-5 {isActive ? 'text-sidebar-accent-foreground' : 'text-sidebar-foreground/70'} group-hover:text-sidebar-accent-foreground transition-colors duration-200" />
											</div>
											<span class="font-medium text-sm {isActive ? 'text-sidebar-accent-foreground' : 'text-sidebar-foreground'} group-hover:text-sidebar-accent-foreground transition-colors duration-200">
												{item.title}
											</span>
											{#if isActive}
												<div class="absolute right-2 w-1.5 h-1.5 rounded-full bg-primary animate-pulse"></div>
											{/if}
										</a>
									{/snippet}
								</SidebarMenuButton>
							</SidebarMenuItem>
						{/each}
					</SidebarMenu>
				</SidebarGroupContent>
			</SidebarGroup>
		</SidebarContent>

		<SidebarFooter class="border-t border-sidebar-border/50 pt-4 pb-2">
			<SidebarMenu>
				<SidebarMenuItem>
					<DropdownMenu.Root>
						<DropdownMenu.Trigger class="w-full">
							<div class="group h-14 px-3 rounded-xl hover:bg-sidebar-accent/80 transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] flex items-center gap-3 w-full cursor-pointer">
								<div class="flex aspect-square size-10 items-center justify-center rounded-xl bg-gradient-to-br from-sidebar-primary to-sidebar-primary/80 text-sidebar-primary-foreground shadow-md group-hover:shadow-lg transition-all duration-200 group-hover:scale-105">
									<span class="text-sm font-bold">{getUserInitials(data.user)}</span>
								</div>
								<div class="grid flex-1 text-left leading-tight ml-2">
									<span class="truncate font-semibold text-sm text-sidebar-foreground group-hover:text-sidebar-accent-foreground transition-colors duration-200">
										{data.user?.name || 'User'}
									</span>
									<span class="truncate text-xs text-sidebar-foreground/60 group-hover:text-sidebar-accent-foreground/80 transition-colors duration-200">
										{data.user?.primaryEmail || ''}
									</span>
								</div>
								<div class="flex items-center justify-center size-6 rounded-md bg-sidebar-accent/20 group-hover:bg-sidebar-accent/40 transition-all duration-200">
									<ChevronsUpDown class="size-3.5 text-sidebar-foreground/60 group-hover:text-sidebar-accent-foreground transition-colors duration-200" />
								</div>
							</div>
						</DropdownMenu.Trigger>
						<DropdownMenu.Content 
							class="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg shadow-lg border border-sidebar-border bg-sidebar p-1"
							side="right"
							align="end"
							sideOffset={4}
						>
							<DropdownMenu.Label class="p-0 font-normal">
								<div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
									<div class="flex size-8 items-center justify-center rounded-lg bg-sidebar-accent text-sidebar-accent-foreground">
										<span class="text-xs font-semibold">{getUserInitials(data.user)}</span>
									</div>
									<div class="grid flex-1 text-left text-sm leading-tight">
										<span class="truncate font-semibold text-sidebar-foreground">
											{data.user?.name || 'User'}
										</span>
										<span class="truncate text-xs text-sidebar-foreground/60">
											{data.user?.primaryEmail || ''}
										</span>
									</div>
								</div>
							</DropdownMenu.Label>
							<DropdownMenu.Separator class="my-1 bg-sidebar-border/50" />
							<DropdownMenu.Item 
								class="flex items-center gap-2 px-2 py-1.5 text-sm cursor-pointer rounded-md hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors"
								onclick={() => window.location.href = '/profile'}
							>
								<User class="size-4" />
								Profile
							</DropdownMenu.Item>
							<DropdownMenu.Item 
								class="flex items-center gap-2 px-2 py-1.5 text-sm cursor-pointer rounded-md hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors"
								onclick={() => window.location.href = '/settings'}
							>
								<Settings class="size-4" />
								Settings
							</DropdownMenu.Item>
							<DropdownMenu.Separator class="my-1 bg-sidebar-border/50" />
							<DropdownMenu.Item 
								class="flex items-center gap-2 px-2 py-1.5 text-sm cursor-pointer rounded-md hover:bg-destructive hover:text-destructive-foreground transition-colors"
								onclick={handleSignOut}
							>
								<LogOut class="size-4" />
								Sign out
							</DropdownMenu.Item>
						</DropdownMenu.Content>
					</DropdownMenu.Root>
				</SidebarMenuItem>
			</SidebarMenu>
		</SidebarFooter>
	</Sidebar>

	<SidebarInset>
		<header class="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-2 border-b border-sidebar-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
			<div class="flex items-center gap-3 px-6">
				<SidebarTrigger class="-ml-1 h-8 w-8 rounded-md hover:bg-sidebar-accent/50 transition-colors duration-200" />
				<div class="h-5 w-px bg-sidebar-border/60"></div>
				<nav class="flex items-center gap-2 text-sm">
					<a href="/dashboard" class="font-semibold text-foreground hover:text-primary transition-colors duration-200">
						Dashboard
					</a>
				</nav>
			</div>
			
			<!-- Right side actions -->
			<div class="ml-auto flex items-center gap-3 px-6">
				<Button variant="outline" size="sm" class="gap-2 h-9 px-4 font-medium hover:bg-primary hover:text-primary-foreground transition-all duration-200 hover:scale-105 active:scale-95">
					<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="transition-transform duration-200">
						<path d="M12 5v14m-7-7h14"/>
					</svg>
					Create
				</Button>
			</div>
		</header>

		<main class="flex flex-1 flex-col gap-6 p-6 pt-4 bg-background/50">
			<div class="mx-auto w-full max-w-7xl">
				{@render children?.()}
			</div>
		</main>
	</SidebarInset>
</SidebarProvider>

<style>
	:global(.sidebar-wrapper) {
		min-height: 100vh;
		background: hsl(var(--background));
		font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}

	/* Enhanced sidebar styling */
	:global([data-sidebar="sidebar"]) {
		background: hsl(var(--sidebar));
		border-right: 1px solid hsl(var(--sidebar-border));
	}

	/* Smooth transitions for all sidebar elements */
	:global([data-sidebar="menu-button"]) {
		transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
	}

	/* Enhanced focus states */
	:global([data-sidebar="menu-button"]:focus-visible) {
		outline: 2px solid hsl(var(--sidebar-ring));
		outline-offset: 2px;
	}

	/* Improved hover effects */
	:global([data-sidebar="menu-button"]:hover) {
		transform: translateX(2px);
	}

	/* Active state enhancements */
	:global([data-sidebar="menu-button"][data-active="true"]) {
		background: hsl(var(--sidebar-accent));
		color: hsl(var(--sidebar-accent-foreground));
		box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
	}

	/* Collapsed state improvements */
	:global(.group[data-collapsible="icon"] [data-sidebar="menu-button"]) {
		justify-content: center;
		padding: 0.5rem;
	}

	/* Header improvements */
	:global([data-slot="sidebar-header"]) {
		padding: 1rem;
		border-bottom: 1px solid hsl(var(--sidebar-border) / 0.5);
	}

	/* Footer improvements */
	:global([data-slot="sidebar-footer"]) {
		padding: 1rem;
		border-top: 1px solid hsl(var(--sidebar-border) / 0.5);
		margin-top: auto;
	}

	/* Content area improvements */
	:global([data-slot="sidebar-content"]) {
		flex: 1;
		overflow-y: auto;
		scrollbar-width: thin;
		scrollbar-color: hsl(var(--sidebar-border)) transparent;
	}

	:global([data-slot="sidebar-content"]::-webkit-scrollbar) {
		width: 6px;
	}

	:global([data-slot="sidebar-content"]::-webkit-scrollbar-track) {
		background: transparent;
	}

	:global([data-slot="sidebar-content"]::-webkit-scrollbar-thumb) {
		background-color: hsl(var(--sidebar-border));
		border-radius: 3px;
	}

	:global([data-slot="sidebar-content"]::-webkit-scrollbar-thumb:hover) {
		background-color: hsl(var(--sidebar-accent));
	}
</style>
