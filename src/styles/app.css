@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.65rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.623 0.214 259.815);
  --primary-foreground: oklch(0.97 0.014 254.604);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.623 0.214 259.815);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.623 0.214 259.815);
  --sidebar-primary-foreground: oklch(0.97 0.014 254.604);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.623 0.214 259.815);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.546 0.245 262.881);
  --primary-foreground: oklch(0.379 0.146 265.522);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.488 0.243 264.376);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.546 0.245 262.881);
  --sidebar-primary-foreground: oklch(0.379 0.146 265.522);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.488 0.243 264.376);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Enhanced sidebar animations */
  .sidebar-animate-in {
    animation: sidebar-slide-in 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar-animate-out {
    animation: sidebar-slide-out 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Custom scrollbar for sidebar */
  .sidebar-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--sidebar-border)) transparent;
  }

  .sidebar-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .sidebar-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .sidebar-scrollbar::-webkit-scrollbar-thumb {
    background-color: hsl(var(--sidebar-border));
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  .sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--sidebar-accent));
  }

  /* Enhanced focus styles */
  .sidebar-focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-sidebar-ring focus-visible:ring-offset-2 focus-visible:ring-offset-sidebar;
  }

  /* Modern glassmorphism effect for sidebar */
  .sidebar-glass {
    background: hsl(var(--sidebar) / 0.8);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid hsl(var(--sidebar-border) / 0.2);
  }

  /* Enhanced menu item hover effects */
  .sidebar-menu-item {
    position: relative;
    overflow: hidden;
  }

  .sidebar-menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, hsl(var(--sidebar-accent) / 0.1), transparent);
    transition: left 0.5s ease;
  }

  .sidebar-menu-item:hover::before {
    left: 100%;
  }

  /* Improved active state indicator */
  .sidebar-active-indicator {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: linear-gradient(to bottom, hsl(var(--primary)), hsl(var(--primary) / 0.6));
    border-radius: 0 2px 2px 0;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .sidebar-menu-item[data-active="true"] .sidebar-active-indicator {
    opacity: 1;
  }

  /* Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .sidebar-animate-in,
    .sidebar-animate-out {
      animation: none;
    }

    .sidebar-menu-item::before {
      display: none;
    }

    * {
      transition-duration: 0.01ms !important;
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
    }
  }
}

@layer utilities {
  /* Custom easing functions for sidebar */
  .ease-sidebar {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .ease-sidebar-bounce {
    transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  /* Responsive sidebar utilities */
  .sidebar-responsive {
    /* Mobile-first approach */
    @apply w-full;

    /* Tablet and up */
    @media (min-width: 768px) {
      @apply w-(--sidebar-width);
    }

    /* Large screens */
    @media (min-width: 1024px) {
      @apply w-(--sidebar-width);
    }

    /* Extra large screens */
    @media (min-width: 1280px) {
      @apply w-(--sidebar-width);
    }
  }

  /* Safe area insets for mobile devices */
  .sidebar-safe-area {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Performance optimizations */
  .sidebar-gpu-accelerated {
    transform: translateZ(0);
    will-change: transform, width, opacity;
  }

  .sidebar-no-gpu {
    will-change: auto;
  }

  /* Optimize for different device capabilities */
  @media (max-width: 768px) {
    .sidebar-mobile-optimized {
      /* Reduce complexity on mobile */
      box-shadow: none;
      backdrop-filter: none;
    }
  }

  /* High refresh rate displays */
  @media (min-resolution: 120dpi) {
    .sidebar-high-dpi {
      /* Optimize for high DPI displays */
      transform: translate3d(0, 0, 0);
    }
  }
}

@keyframes sidebar-slide-in {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes sidebar-slide-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}